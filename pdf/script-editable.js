// 可编辑PDF拆分系统 JavaScript

// 全局变量
let pdfDoc = null;
let currentPage = 1;
let totalPages = 0;
let scale = 1.0;
let currentFile = null;

// 竖线检测相关变量
let lineDetectionEnabled = false;
let detectedLines = [];
let detectionSensitivity = 0.5; // 默认50%

// PDF拆分相关变量
let splitPdfData = null;
let splitInProgress = false;

// DOM元素
let uploadArea, fileInput, fileInfo, fileName, fileSize, removeBtn;
let previewSection, pdfCanvas, ctx;
let currentPageSpan, totalPagesSpan, prevPageBtn, nextPageBtn;
let zoomLevel, zoomInBtn, zoomOutBtn;
let detectLinesBtn, sensitivityControl, sensitivitySlider, sensitivityValue;
let splitPdfBtn, splitModeSelector;
let testResults, testContent;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    setupPdfJs();
});

// 初始化DOM元素
function initializeElements() {
    uploadArea = document.getElementById('uploadArea');
    fileInput = document.getElementById('fileInput');
    fileInfo = document.getElementById('fileInfo');
    fileName = document.getElementById('fileName');
    fileSize = document.getElementById('fileSize');
    removeBtn = document.getElementById('removeBtn');
    
    previewSection = document.getElementById('previewSection');
    pdfCanvas = document.getElementById('pdfCanvas');
    ctx = pdfCanvas.getContext('2d');
    
    currentPageSpan = document.getElementById('currentPageSpan');
    totalPagesSpan = document.getElementById('totalPagesSpan');
    prevPageBtn = document.getElementById('prevPageBtn');
    nextPageBtn = document.getElementById('nextPageBtn');
    
    zoomLevel = document.getElementById('zoomLevel');
    zoomInBtn = document.getElementById('zoomInBtn');
    zoomOutBtn = document.getElementById('zoomOutBtn');
    
    detectLinesBtn = document.getElementById('detectLinesBtn');
    sensitivityControl = document.getElementById('sensitivityControl');
    sensitivitySlider = document.getElementById('sensitivitySlider');
    sensitivityValue = document.getElementById('sensitivityValue');
    
    splitPdfBtn = document.getElementById('splitPdfBtn');
    splitModeSelector = document.getElementById('splitModeSelector');
    
    testResults = document.getElementById('testResults');
    testContent = document.getElementById('testContent');
}

// 设置事件监听器
function setupEventListeners() {
    // 文件上传相关
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    removeBtn.addEventListener('click', handleRemoveFile);
    
    // 页面导航
    prevPageBtn.addEventListener('click', () => changePage(-1));
    nextPageBtn.addEventListener('click', () => changePage(1));
    
    // 缩放控制
    zoomInBtn.addEventListener('click', () => changeZoom(0.2));
    zoomOutBtn.addEventListener('click', () => changeZoom(-0.2));
    
    // 竖线检测
    detectLinesBtn.addEventListener('click', toggleLineDetection);
    sensitivitySlider.addEventListener('input', updateSensitivity);
    
    // PDF拆分
    splitPdfBtn.addEventListener('click', splitPdfByLines);
}

// 设置 PDF.js worker
function setupPdfJs() {
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
        processFile(file);
    } else {
        alert('请选择有效的 PDF 文件！');
    }
}

// 处理拖拽
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type === 'application/pdf') {
            processFile(file);
        } else {
            alert('请选择有效的 PDF 文件！');
        }
    }
}

// 处理文件
function processFile(file) {
    currentFile = file;
    
    // 显示文件信息
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.style.display = 'block';
    
    // 加载 PDF
    loadPDF(file);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载 PDF
async function loadPDF(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        pdfDoc = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdfDoc.numPages;
        currentPage = 1;
        
        // 更新页面信息
        totalPagesSpan.textContent = totalPages;
        currentPageSpan.textContent = currentPage;
        
        // 显示预览区域
        previewSection.style.display = 'block';
        
        // 渲染第一页
        renderPage(currentPage);
        
        // 更新控制按钮状态
        updateControls();
        
    } catch (error) {
        console.error('加载 PDF 失败:', error);
        alert('加载 PDF 文件失败，请确保文件完整且未损坏。');
    }
}

// 渲染页面
async function renderPage(pageNum) {
    try {
        const page = await pdfDoc.getPage(pageNum);
        const viewport = page.getViewport({ scale: scale });
        
        // 设置 canvas 尺寸
        pdfCanvas.height = viewport.height;
        pdfCanvas.width = viewport.width;
        
        // 渲染页面
        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };
        
        await page.render(renderContext).promise;

        // 如果启用了竖线检测，重新绘制检测结果
        if (lineDetectionEnabled && detectedLines.length > 0) {
            drawDetectedLines();
        }

    } catch (error) {
        console.error('渲染页面失败:', error);
        alert('渲染页面失败！');
    }
}

// 更新控制按钮状态
function updateControls() {
    prevPageBtn.disabled = currentPage <= 1;
    nextPageBtn.disabled = currentPage >= totalPages;
}

// 切换页面
function changePage(delta) {
    const newPage = currentPage + delta;
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        currentPageSpan.textContent = currentPage;
        renderPage(currentPage);
        updateControls();
        
        // 清除之前的检测结果
        if (lineDetectionEnabled) {
            detectedLines = [];
            splitPdfBtn.style.display = 'none';
        }
    }
}

// 改变缩放
function changeZoom(delta) {
    const newScale = Math.max(0.5, Math.min(3.0, scale + delta));
    if (newScale !== scale) {
        scale = newScale;
        zoomLevel.textContent = Math.round(scale * 100) + '%';
        renderPage(currentPage);
    }
}

// 移除文件
function handleRemoveFile() {
    currentFile = null;
    pdfDoc = null;
    fileInput.value = '';
    fileInfo.style.display = 'none';
    previewSection.style.display = 'none';
    splitModeSelector.style.display = 'none';
    testResults.classList.remove('show');

    // 清空 canvas
    ctx.clearRect(0, 0, pdfCanvas.width, pdfCanvas.height);

    // 重置变量
    currentPage = 1;
    totalPages = 0;
    scale = 1.0;
    zoomLevel.textContent = '100%';

    // 清理拆分相关数据
    splitPdfData = null;
    splitInProgress = false;
    lineDetectionEnabled = false;
    detectedLines = [];
    
    // 重置按钮状态
    detectLinesBtn.innerHTML = '<i class="fas fa-search"></i> 检测竖线';
    splitPdfBtn.style.display = 'none';
    sensitivityControl.style.display = 'none';
}

// 切换竖线检测
async function toggleLineDetection() {
    if (!pdfDoc || !currentFile) {
        alert('请先上传PDF文件！');
        return;
    }

    if (!lineDetectionEnabled) {
        // 启用检测
        detectLinesBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检测中...';
        detectLinesBtn.disabled = true;
        
        try {
            await detectVerticalLines();
            lineDetectionEnabled = true;
            detectLinesBtn.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏竖线';
            sensitivityControl.style.display = 'block';
            
            if (detectedLines.length > 0) {
                splitPdfBtn.style.display = 'inline-block';
                splitModeSelector.style.display = 'block';
            }
        } catch (error) {
            console.error('竖线检测失败:', error);
            alert('竖线检测失败: ' + error.message);
            detectLinesBtn.innerHTML = '<i class="fas fa-search"></i> 检测竖线';
        }
        
        detectLinesBtn.disabled = false;
    } else {
        // 禁用检测
        lineDetectionEnabled = false;
        detectedLines = [];
        detectLinesBtn.innerHTML = '<i class="fas fa-search"></i> 检测竖线';
        sensitivityControl.style.display = 'none';
        splitPdfBtn.style.display = 'none';
        splitModeSelector.style.display = 'none';
        renderPage(currentPage); // 重新渲染以清除线条
    }
}

// 检测竖线
async function detectVerticalLines() {
    const formData = new FormData();
    formData.append('pdf', currentFile);
    formData.append('pageNumber', currentPage);
    formData.append('sensitivity', detectionSensitivity);

    const response = await fetch('/api/detect-lines', {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        throw new Error(`服务器错误: ${response.status}`);
    }

    const result = await response.json();
    detectedLines = result.lines || [];
    
    console.log('检测到竖线:', detectedLines.length);
    
    // 重新渲染页面以显示检测结果
    renderPage(currentPage);
}

// 绘制检测到的竖线
function drawDetectedLines() {
    if (!detectedLines || detectedLines.length === 0) return;
    
    ctx.save();
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    
    detectedLines.forEach(line => {
        const x = (line.x / 150) * scale * 72; // 转换坐标
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, pdfCanvas.height);
        ctx.stroke();
    });
    
    ctx.restore();
}

// 更新敏感度
function updateSensitivity() {
    detectionSensitivity = sensitivitySlider.value / 100;
    sensitivityValue.textContent = sensitivitySlider.value + '%';
    
    if (lineDetectionEnabled) {
        // 重新检测
        detectVerticalLines();
    }
}

// PDF拆分功能
async function splitPdfByLines() {
    if (!pdfDoc || !currentFile || detectedLines.length === 0 || splitInProgress) {
        return;
    }

    try {
        splitInProgress = true;
        splitPdfBtn.disabled = true;
        splitPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 拆分中...';

        // 选择最佳的竖线作为分割线
        const splitLine = selectBestSplitLine();
        if (!splitLine) {
            throw new Error('未找到合适的分割线');
        }

        // 获取选择的拆分模式
        const splitMode = document.querySelector('input[name="splitMode"]:checked').value;
        const preserveText = splitMode === 'vector';
        
        console.log('拆分模式:', splitMode, '保持文本:', preserveText);

        // 调用服务端拆分API
        await splitPdfServerSide(splitLine, preserveText);

        // 显示拆分结果
        showSplitResult(splitLine, preserveText);

    } catch (error) {
        console.error('PDF拆分失败:', error);
        alert('PDF拆分失败: ' + error.message);
    } finally {
        splitInProgress = false;
        splitPdfBtn.disabled = false;
        splitPdfBtn.innerHTML = '<i class="fas fa-cut"></i> 拆分PDF';
    }
}

// 选择最佳分割线
function selectBestSplitLine() {
    if (detectedLines.length === 0) return null;
    
    // 根据置信度和位置选择最佳线条
    const page = pdfDoc.getPages()[currentPage - 1];
    const { width } = page.getSize();
    const centerX = width / 2;
    
    let bestLine = null;
    let bestScore = 0;
    
    detectedLines.forEach(line => {
        const confidenceScore = line.confidence * 0.7;
        const positionScore = (1 - Math.abs(line.x - centerX) / centerX) * 0.3;
        const totalScore = confidenceScore + positionScore;
        
        if (totalScore > bestScore) {
            bestScore = totalScore;
            bestLine = line;
        }
    });
    
    return bestLine;
}

// 服务端拆分
async function splitPdfServerSide(splitLine, preserveText) {
    const formData = new FormData();
    formData.append('pdf', currentFile);
    formData.append('pageNumber', currentPage);
    formData.append('splitX', splitLine.x);
    formData.append('preserveText', preserveText);

    const response = await fetch('/api/split-pdf-editable', {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        throw new Error(`服务器错误: ${response.status}`);
    }

    // 获取PDF数据
    splitPdfData = await response.arrayBuffer();
}

// 显示拆分结果
function showSplitResult(splitLine, preserveText) {
    const mode = preserveText ? '矢量拆分（可编辑）' : '图像拆分（传统）';
    const features = preserveText ? 
        '✅ 文本可选择和编辑<br>✅ 支持文本搜索<br>✅ 保持矢量质量' :
        '✅ 完美视觉效果<br>❌ 文本变为图像<br>❌ 无法编辑文本';
    
    testContent.innerHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h5>拆分信息</h5>
                <p><strong>模式:</strong> ${mode}</p>
                <p><strong>分割位置:</strong> ${Math.round(splitLine.x)}px</p>
                <p><strong>置信度:</strong> ${Math.round(splitLine.confidence * 100)}%</p>
            </div>
            <div>
                <h5>功能特性</h5>
                <p>${features}</p>
            </div>
        </div>
        <div style="text-align: center; margin-top: 15px;">
            <button onclick="downloadSplitPdf()" class="action-btn">
                <i class="fas fa-download"></i> 下载拆分PDF
            </button>
        </div>
    `;
    
    testResults.classList.add('show');
}

// 下载拆分后的PDF
function downloadSplitPdf() {
    if (!splitPdfData) {
        alert('没有可下载的拆分PDF数据');
        return;
    }

    try {
        const blob = new Blob([splitPdfData], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${currentFile.name.replace('.pdf', '')}_split_editable.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('可编辑拆分PDF下载完成');
    } catch (error) {
        console.error('下载拆分PDF失败:', error);
        alert('下载失败: ' + error.message);
    }
}
