<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可编辑PDF拆分系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        .editable-features {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .feature-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 2px solid transparent;
        }
        
        .comparison-card.recommended {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
        }
        
        .comparison-card h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .comparison-card .pros {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .comparison-card .cons {
            color: #dc3545;
        }
        
        .split-mode-selector {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .mode-option {
            display: flex;
            align-items: center;
            margin: 10px 0;
            cursor: pointer;
        }
        
        .mode-option input[type="radio"] {
            margin-right: 10px;
        }
        
        .mode-option label {
            cursor: pointer;
            font-weight: 500;
        }
        
        .mode-description {
            font-size: 0.9em;
            color: #666;
            margin-left: 25px;
            margin-top: 5px;
        }
        
        .test-results {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        
        .test-results.show {
            display: block;
        }
        
        .test-results h4 {
            color: #28a745;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-file-pdf"></i> 可编辑PDF拆分系统</h1>
            <p>保持文本可编辑性的智能PDF拆分工具</p>
        </header>

        <div class="editable-features">
            <h2><i class="fas fa-magic"></i> 新功能：保持文本可编辑性</h2>
            <p>现在支持矢量拆分，拆分后的PDF文件依然可以选择和编辑文本！</p>
        </div>

        <div class="feature-comparison">
            <div class="comparison-card recommended">
                <h3><i class="fas fa-star"></i> 矢量拆分（推荐）</h3>
                <div class="pros">
                    <i class="fas fa-check"></i> 保持文本可编辑性<br>
                    <i class="fas fa-check"></i> 保持矢量图形质量<br>
                    <i class="fas fa-check"></i> 文件大小更小<br>
                    <i class="fas fa-check"></i> 支持文本搜索
                </div>
                <div class="cons">
                    <i class="fas fa-times"></i> 复杂布局可能有偏差
                </div>
            </div>
            
            <div class="comparison-card">
                <h3><i class="fas fa-image"></i> 图像拆分（传统）</h3>
                <div class="pros">
                    <i class="fas fa-check"></i> 完美保持视觉效果<br>
                    <i class="fas fa-check"></i> 适用于所有PDF类型
                </div>
                <div class="cons">
                    <i class="fas fa-times"></i> 文本变为图像，无法编辑<br>
                    <i class="fas fa-times"></i> 文件大小较大<br>
                    <i class="fas fa-times"></i> 无法搜索文本
                </div>
            </div>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击选择PDF文件或拖拽到此处</p>
                <input type="file" id="fileInput" accept=".pdf" style="display: none;">
            </div>
            
            <div class="file-info" id="fileInfo" style="display: none;">
                <div class="file-details">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                        <div class="file-name" id="fileName"></div>
                        <div class="file-size" id="fileSize"></div>
                    </div>
                    <button class="remove-btn" id="removeBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- PDF预览区域 -->
        <div class="preview-section" id="previewSection" style="display: none;">
            <div class="preview-header">
                <h3>PDF预览</h3>
                <div class="preview-controls">
                    <button id="prevPageBtn"><i class="fas fa-chevron-left"></i></button>
                    <span>第 <span id="currentPageSpan">1</span> 页，共 <span id="totalPagesSpan">1</span> 页</span>
                    <button id="nextPageBtn"><i class="fas fa-chevron-right"></i></button>
                    <div class="zoom-controls">
                        <button id="zoomOutBtn"><i class="fas fa-search-minus"></i></button>
                        <span id="zoomLevel">100%</span>
                        <button id="zoomInBtn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="canvas-container">
                <canvas id="pdfCanvas"></canvas>
            </div>
            
            <div class="action-buttons">
                <button id="detectLinesBtn" class="action-btn">
                    <i class="fas fa-search"></i> 检测竖线
                </button>
                
                <div class="sensitivity-control" id="sensitivityControl" style="display: none;">
                    <label>检测敏感度: <span id="sensitivityValue">50%</span></label>
                    <input type="range" id="sensitivitySlider" min="10" max="100" value="50">
                </div>
                
                <button id="splitPdfBtn" class="action-btn" style="display: none;">
                    <i class="fas fa-cut"></i> 拆分PDF
                </button>
            </div>
        </div>

        <!-- 拆分模式选择 -->
        <div class="split-mode-selector" id="splitModeSelector" style="display: none;">
            <h4><i class="fas fa-cog"></i> 选择拆分模式</h4>
            
            <div class="mode-option">
                <input type="radio" id="vectorMode" name="splitMode" value="vector" checked>
                <label for="vectorMode">矢量拆分（保持可编辑性）</label>
            </div>
            <div class="mode-description">
                推荐选择：保持文本可选择和可编辑，支持搜索功能
            </div>
            
            <div class="mode-option">
                <input type="radio" id="imageMode" name="splitMode" value="image">
                <label for="imageMode">图像拆分（传统方式）</label>
            </div>
            <div class="mode-description">
                适用于复杂布局：转换为图像，视觉效果完美但无法编辑文本
            </div>
        </div>

        <!-- 测试结果展示 -->
        <div class="test-results" id="testResults">
            <h4><i class="fas fa-clipboard-check"></i> 拆分结果测试</h4>
            <div id="testContent">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- PDF.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <!-- PDF-lib Library for PDF creation -->
    <script src="https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js"></script>
    <script src="script-editable.js"></script>
</body>
</html>
