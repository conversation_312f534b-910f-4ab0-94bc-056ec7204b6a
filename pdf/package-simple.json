{"name": "pdf-split-system-simple", "version": "1.0.0", "description": "PDF竖线检测和拆分系统 - 简化版", "main": "server-simple.js", "scripts": {"start": "node server-simple.js", "dev": "nodemon server-simple.js", "install-deps": "npm install", "test": "node -e \"console.log('Server test'); require('./server-simple.js')\""}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "pdf-lib": "^1.17.1", "sharp": "^0.32.6", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["pdf", "split", "line-detection", "document-processing", "simple"], "author": "技术平台团队", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}