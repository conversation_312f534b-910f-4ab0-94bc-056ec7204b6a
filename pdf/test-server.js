// 服务端测试脚本
const http = require('http');

const testServerHealth = () => {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/health',
            method: 'GET',
            timeout: 5000
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (response.status === 'ok') {
                        resolve(response);
                    } else {
                        reject(new Error('服务器状态异常'));
                    }
                } catch (error) {
                    reject(new Error('响应格式错误'));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
};

const testServerInfo = () => {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/info',
            method: 'GET',
            timeout: 5000
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    resolve(response);
                } catch (error) {
                    reject(new Error('响应格式错误'));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
};

async function runTests() {
    console.log('🧪 开始测试PDF处理服务器...\n');

    try {
        // 测试健康检查
        console.log('1. 测试健康检查 API...');
        const healthResponse = await testServerHealth();
        console.log('✅ 健康检查通过');
        console.log(`   状态: ${healthResponse.status}`);
        console.log(`   时间: ${healthResponse.timestamp}\n`);

        // 测试服务器信息
        console.log('2. 测试服务器信息 API...');
        const infoResponse = await testServerInfo();
        console.log('✅ 服务器信息获取成功');
        console.log(`   名称: ${infoResponse.name}`);
        console.log(`   版本: ${infoResponse.version}`);
        console.log(`   功能: ${infoResponse.features.join(', ')}`);
        console.log(`   最大文件大小: ${infoResponse.maxFileSize}\n`);

        console.log('🎉 所有测试通过！服务器运行正常。');
        console.log('\n📝 接下来你可以：');
        console.log('1. 在浏览器中访问 http://localhost:3000');
        console.log('2. 点击右上角的模式切换按钮选择"服务端模式"');
        console.log('3. 上传PDF文件并测试拆分功能');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('\n🔧 可能的解决方案：');
        console.log('1. 确保服务器已启动: node server-simple.js');
        console.log('2. 检查端口3000是否被占用');
        console.log('3. 确保防火墙没有阻止连接');
        process.exit(1);
    }
}

// 运行测试
runTests();
