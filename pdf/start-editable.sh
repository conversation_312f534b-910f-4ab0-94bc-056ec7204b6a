#!/bin/bash

# 可编辑PDF拆分系统启动脚本

echo "🚀 启动可编辑PDF拆分系统"
echo "================================"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

# 启动服务器
echo "🌟 启动可编辑PDF拆分服务器..."
echo "📍 服务器地址: http://localhost:3001"
echo "📄 可编辑拆分页面: http://localhost:3001/index-editable.html"
echo "📄 传统拆分页面: http://localhost:3001/index.html"
echo ""
echo "💡 使用 Ctrl+C 停止服务器"
echo "================================"

# 启动服务器
node server-editable.js
