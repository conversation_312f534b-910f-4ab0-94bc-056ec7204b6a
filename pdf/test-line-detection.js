// 测试竖线检测功能

const fs = require('fs');
const { PDFDocument, rgb } = require('pdf-lib');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function createTestPDFWithLines() {
    console.log('🧪 创建带竖线的测试PDF...');
    
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([600, 400]);
    
    // 添加左侧内容
    page.drawText('LEFT CONTENT', {
        x: 50,
        y: 300,
        size: 20,
        color: rgb(0, 0, 1),
    });
    
    page.drawText('This is left side text content', {
        x: 50,
        y: 250,
        size: 12,
        color: rgb(0, 0, 0),
    });
    
    // 添加明显的竖线
    page.drawLine({
        start: { x: 300, y: 50 },
        end: { x: 300, y: 350 },
        thickness: 3,
        color: rgb(0, 0, 0),
    });
    
    // 添加第二条竖线
    page.drawLine({
        start: { x: 450, y: 80 },
        end: { x: 450, y: 320 },
        thickness: 2,
        color: rgb(0.5, 0.5, 0.5),
    });
    
    // 添加右侧内容
    page.drawText('RIGHT CONTENT', {
        x: 350,
        y: 300,
        size: 20,
        color: rgb(1, 0, 0),
    });
    
    page.drawText('This is right side text content', {
        x: 350,
        y: 250,
        size: 12,
        color: rgb(0, 0, 0),
    });
    
    // 保存测试PDF
    const pdfBytes = await pdfDoc.save();
    fs.writeFileSync('./test-lines.pdf', pdfBytes);
    
    console.log('✅ 测试PDF已创建: test-lines.pdf');
    return './test-lines.pdf';
}

async function testLineDetection() {
    console.log('🔍 测试竖线检测API...');
    
    try {
        // 创建测试PDF
        const pdfPath = await createTestPDFWithLines();
        
        // 准备表单数据
        const form = new FormData();
        form.append('pdf', fs.createReadStream(pdfPath));
        form.append('pageNumber', '1');
        form.append('sensitivity', '0.5');
        
        // 发送请求到竖线检测API
        const response = await fetch('http://localhost:3001/api/detect-lines', {
            method: 'POST',
            body: form
        });
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        const result = await response.json();
        
        console.log('✅ 竖线检测成功！');
        console.log(`📊 检测到 ${result.lines.length} 条竖线`);
        
        if (result.lines.length > 0) {
            console.log('📋 检测结果详情:');
            result.lines.forEach((line, index) => {
                console.log(`  线条 ${index + 1}: x=${line.x}, 置信度=${(line.confidence * 100).toFixed(1)}%`);
            });
            
            // 测试拆分功能
            await testSplitWithDetectedLines(pdfPath, result.lines[0]);
        } else {
            console.log('⚠️ 未检测到竖线，可能需要调整敏感度');
        }
        
    } catch (error) {
        console.error('❌ 竖线检测测试失败:', error.message);
    }
}

async function testSplitWithDetectedLines(pdfPath, bestLine) {
    console.log('\n✂️ 测试可编辑PDF拆分...');
    
    try {
        // 准备表单数据
        const form = new FormData();
        form.append('pdf', fs.createReadStream(pdfPath));
        form.append('pageNumber', '1');
        form.append('splitX', bestLine.x.toString());
        form.append('preserveText', 'true');
        
        // 发送请求到拆分API
        const response = await fetch('http://localhost:3001/api/split-pdf-editable', {
            method: 'POST',
            body: form
        });
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        // 保存拆分结果
        const pdfBuffer = await response.arrayBuffer();
        fs.writeFileSync('./test-split-editable.pdf', Buffer.from(pdfBuffer));
        
        console.log('✅ 可编辑PDF拆分成功！');
        console.log('📄 拆分结果已保存: test-split-editable.pdf');
        
        // 验证拆分结果
        await verifySplitResult('./test-split-editable.pdf');
        
    } catch (error) {
        console.error('❌ PDF拆分测试失败:', error.message);
    }
}

async function verifySplitResult(filePath) {
    console.log('\n🔍 验证拆分结果...');
    
    try {
        const pdfBytes = fs.readFileSync(filePath);
        const doc = await PDFDocument.load(pdfBytes);
        
        const pages = doc.getPages();
        console.log(`📄 页面数量: ${pages.length} (应为2)`);
        
        if (pages.length === 2) {
            console.log('✅ 页面数量正确');
            
            const page1Size = pages[0].getSize();
            const page2Size = pages[1].getSize();
            
            console.log(`📏 第1页: ${page1Size.width} x ${page1Size.height}`);
            console.log(`📏 第2页: ${page2Size.width} x ${page2Size.height}`);
            
            console.log('\n🎉 所有测试完成！');
            console.log('📝 请手动验证:');
            console.log('   1. 打开 test-split-editable.pdf');
            console.log('   2. 检查是否有2页');
            console.log('   3. 尝试选择文本');
            console.log('   4. 验证文本可复制和搜索');
            
        } else {
            console.log('❌ 页面数量不正确');
        }
        
    } catch (error) {
        console.error('❌ 验证失败:', error.message);
    }
}

// 清理测试文件
function cleanup() {
    console.log('\n🧹 清理测试文件...');
    
    const testFiles = [
        './test-lines.pdf',
        './test-split-editable.pdf'
    ];
    
    testFiles.forEach(file => {
        if (fs.existsSync(file)) {
            fs.unlinkSync(file);
            console.log(`🗑️ 已删除: ${file}`);
        }
    });
}

// 主函数
async function runTest() {
    console.log('🚀 开始竖线检测和可编辑拆分测试\n');
    
    try {
        await testLineDetection();
        
        console.log('\n❓ 测试文件保留以供手动验证');
        console.log('💡 如需清理，请运行: node test-line-detection.js --cleanup');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 命令行参数处理
if (process.argv.includes('--cleanup')) {
    cleanup();
} else {
    runTest();
}
