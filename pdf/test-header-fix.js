// 测试HTTP头部修复
const http = require('http');

// 测试数据 - 包含各种可能导致问题的文件名
const testCases = [
    {
        name: '中文文件名测试',
        filename: '第二册 面积_split.pdf'
    },
    {
        name: '特殊字符测试',
        filename: 'test<>file|name*.pdf'
    },
    {
        name: '换行符测试',
        filename: 'test\nfile\rname.pdf'
    },
    {
        name: '制表符测试',
        filename: 'test\tfile\tname.pdf'
    },
    {
        name: '引号测试',
        filename: 'test"file\'name.pdf'
    }
];

// 基础测试数据
const baseTestData = {
    leftImageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    rightImageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
};

function testSingleCase(testCase) {
    return new Promise((resolve, reject) => {
        const testData = {
            ...baseTestData,
            filename: testCase.filename
        };
        
        const postData = JSON.stringify(testData);
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/split-pdf-images',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            },
            timeout: 10000
        };

        console.log(`\n🧪 测试: ${testCase.name}`);
        console.log(`   原始文件名: "${testCase.filename}"`);

        const req = http.request(options, (res) => {
            let data = Buffer.alloc(0);
            
            res.on('data', (chunk) => {
                data = Buffer.concat([data, chunk]);
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log(`   ✅ 成功! 状态码: ${res.statusCode}`);
                    console.log(`   📁 Content-Disposition: ${res.headers['content-disposition']}`);
                    console.log(`   📊 文件大小: ${data.length} bytes`);
                    resolve({ success: true, headers: res.headers, size: data.length });
                } else {
                    try {
                        const errorResponse = JSON.parse(data.toString());
                        console.log(`   ❌ 失败! 状态码: ${res.statusCode}`);
                        console.log(`   📝 错误信息: ${errorResponse.error}`);
                        resolve({ success: false, error: errorResponse.error, statusCode: res.statusCode });
                    } catch (e) {
                        console.log(`   ❌ 失败! 状态码: ${res.statusCode}`);
                        console.log(`   📝 响应: ${data.toString()}`);
                        resolve({ success: false, error: data.toString(), statusCode: res.statusCode });
                    }
                }
            });
        });

        req.on('error', (error) => {
            console.log(`   ❌ 请求失败: ${error.message}`);
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            console.log(`   ❌ 请求超时`);
            reject(new Error('请求超时'));
        });

        req.write(postData);
        req.end();
    });
}

async function runAllTests() {
    console.log('🚀 开始测试HTTP头部修复效果...');
    
    try {
        // 首先检查服务器状态
        console.log('\n1️⃣ 检查服务器状态...');
        await new Promise((resolve, reject) => {
            const req = http.get('http://localhost:3000/api/health', (res) => {
                if (res.statusCode === 200) {
                    console.log('   ✅ 服务器运行正常');
                    resolve();
                } else {
                    reject(new Error('服务器状态异常'));
                }
            });
            req.on('error', reject);
            req.setTimeout(5000, () => {
                req.destroy();
                reject(new Error('服务器连接超时'));
            });
        });

        // 运行所有测试用例
        console.log('\n2️⃣ 运行文件名测试用例...');
        
        let successCount = 0;
        let failCount = 0;
        
        for (const testCase of testCases) {
            try {
                const result = await testSingleCase(testCase);
                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                console.log(`   ❌ 测试异常: ${error.message}`);
                failCount++;
            }
        }
        
        // 输出测试结果
        console.log('\n📊 测试结果汇总:');
        console.log(`   ✅ 成功: ${successCount} 个`);
        console.log(`   ❌ 失败: ${failCount} 个`);
        console.log(`   📈 成功率: ${Math.round(successCount / testCases.length * 100)}%`);
        
        if (failCount === 0) {
            console.log('\n🎉 所有测试通过! HTTP头部错误已完全修复!');
        } else {
            console.log('\n⚠️  部分测试失败，需要进一步检查');
        }
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\n💡 解决方案:');
            console.log('   1. 启动服务器: node server-simple.js');
            console.log('   2. 确保端口3000未被占用');
        }
        
        process.exit(1);
    }
}

// 运行测试
runAllTests();
