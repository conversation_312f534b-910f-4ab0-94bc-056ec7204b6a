// 测试服务端修复效果
const http = require('http');

// 测试数据 - 模拟前端发送的数据
const testData = {
    leftImageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    rightImageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    filename: '第二册 面积_split.pdf'  // 包含中文和空格的文件名
};

function testServerSplit() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify(testData);
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/split-pdf-images',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            },
            timeout: 30000
        };

        const req = http.request(options, (res) => {
            console.log(`状态码: ${res.statusCode}`);
            console.log(`响应头:`, res.headers);
            
            let data = Buffer.alloc(0);
            
            res.on('data', (chunk) => {
                data = Buffer.concat([data, chunk]);
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ PDF拆分成功！');
                    console.log(`文件大小: ${data.length} bytes`);
                    console.log(`Content-Disposition: ${res.headers['content-disposition']}`);
                    resolve({ success: true, size: data.length, headers: res.headers });
                } else {
                    try {
                        const errorResponse = JSON.parse(data.toString());
                        console.log('❌ 服务端返回错误:', errorResponse);
                        reject(new Error(errorResponse.error));
                    } catch (e) {
                        console.log('❌ 服务端返回错误:', data.toString());
                        reject(new Error('服务端错误'));
                    }
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求失败:', error.message);
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            console.error('❌ 请求超时');
            reject(new Error('请求超时'));
        });

        req.write(postData);
        req.end();
    });
}

async function runTest() {
    console.log('🧪 测试服务端PDF拆分功能...\n');
    console.log('测试文件名:', testData.filename);
    
    try {
        // 首先检查服务器是否运行
        console.log('1. 检查服务器状态...');
        const healthCheck = await new Promise((resolve, reject) => {
            const req = http.get('http://localhost:3000/api/health', (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    if (res.statusCode === 200) {
                        resolve(JSON.parse(data));
                    } else {
                        reject(new Error('服务器不健康'));
                    }
                });
            });
            req.on('error', reject);
            req.setTimeout(5000, () => {
                req.destroy();
                reject(new Error('健康检查超时'));
            });
        });
        
        console.log('✅ 服务器运行正常');
        console.log(`   状态: ${healthCheck.status}`);
        
        // 测试PDF拆分
        console.log('\n2. 测试PDF拆分...');
        const result = await testServerSplit();
        
        console.log('\n🎉 测试完成！');
        console.log('✅ 文件名包含中文字符的HTTP头部错误已修复');
        console.log('✅ 服务端可以正确处理中文文件名');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\n💡 解决方案：');
            console.log('1. 启动服务器: node server-simple.js');
            console.log('2. 确保端口3000未被占用');
        } else if (error.message.includes('Invalid character in header')) {
            console.log('\n💡 这正是我们要修复的问题！');
            console.log('请确保使用了修复后的server-simple.js');
        }
        
        process.exit(1);
    }
}

// 运行测试
runTest();
