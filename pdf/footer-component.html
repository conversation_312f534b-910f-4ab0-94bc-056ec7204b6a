<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术网站 Footer 组件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        .footer {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }
        
        .footer-section h4 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 8px;
            font-weight: 600;
        }
        
        .footer-section ul {
            list-style: none;
            padding: 0;
        }
        
        .footer-section li {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .footer-section li:hover {
            transform: translateX(5px);
        }
        
        .footer-section a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .footer-section a:hover {
            color: #667eea;
        }
        
        .footer-section i {
            color: #667eea;
            width: 18px;
            font-size: 1rem;
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 25px;
            border-top: 2px solid #eee;
            color: #666;
            font-size: 0.95rem;
        }
        
        .team-info {
            color: #555;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .footer {
                padding: 25px;
            }
            
            .footer-content {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .footer-section h4 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h4><i class="fas fa-headset"></i> 支持</h4>
                <ul>
                    <li>
                        <a href="#" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            开放平台
                        </a>
                    </li>
                    <li>
                        <a href="#" target="_blank">
                            <i class="fas fa-file-contract"></i>
                            开发者协议
                        </a>
                    </li>
                    <li>
                        <a href="#" target="_blank">
                            <i class="fas fa-envelope"></i>
                            联系我们
                        </a>
                    </li>
                    <li>
                        <a href="#" target="_blank">
                            <i class="fas fa-comments"></i>
                            在线客服
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="footer-section">
                <h4><i class="fas fa-users"></i> 团队</h4>
                <ul>
                    <li class="team-info">
                        <i class="fas fa-building"></i>
                        社交平台架构中心
                    </li>
                    <li class="team-info">
                        <i class="fas fa-cog"></i>
                        基础架构组一组
                    </li>
                    <li class="team-info">
                        <i class="fas fa-server"></i>
                        基础架构五组
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2024 技术平台. 保留所有权利. | 由社交平台架构中心提供技术支持</p>
        </div>
    </footer>
</body>
</html>
