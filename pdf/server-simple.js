const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const sharp = require('sharp');
const cors = require('cors');

const app = express();
const port = 3000;

// 文件名清理函数（HTTP头部安全）
function sanitizeFilename(filename) {
    if (!filename) return 'split-document.pdf';

    // 只保留ASCII字符，确保HTTP头部安全
    let safeName = filename
        .replace(/[\r\n\t]/g, '_')      // 替换控制字符
        .replace(/[^\x20-\x7E]/g, '_')  // 替换所有非ASCII字符为下划线
        .replace(/[<>:"|?*\\]/g, '_')   // 替换不安全字符
        .replace(/\s+/g, '_')           // 替换空格为下划线
        .replace(/_{2,}/g, '_')         // 合并多个下划线
        .trim();

    // 移除开头和结尾的下划线
    safeName = safeName.replace(/^_+|_+$/g, '');

    // 确保文件名不为空
    if (!safeName || safeName === '_') {
        safeName = 'split-document';
    }

    // 确保有.pdf扩展名
    if (!safeName.toLowerCase().endsWith('.pdf')) {
        safeName += '.pdf';
    }

    // 限制文件名长度
    if (safeName.length > 100) {
        const nameWithoutExt = safeName.substring(0, safeName.lastIndexOf('.pdf'));
        safeName = nameWithoutExt.substring(0, 96) + '.pdf';
    }

    return safeName;
}

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.static('.'));

// 配置文件上传
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = './uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    }
});

// 简化的PDF拆分API - 接收前端处理的图像数据
app.post('/api/split-pdf-images', express.json({ limit: '50mb' }), async (req, res) => {
    try {
        const { leftImageData, rightImageData, filename } = req.body;

        console.log('接收到图像数据进行PDF拆分');
        console.log('原始文件名:', filename);

        if (!leftImageData || !rightImageData) {
            throw new Error('缺少图像数据');
        }

        // 验证图像数据格式
        if (!leftImageData.startsWith('data:image/png;base64,') ||
            !rightImageData.startsWith('data:image/png;base64,')) {
            throw new Error('图像数据格式不正确');
        }
        
        // 创建新的PDF文档
        const pdfDoc = await PDFDocument.create();
        
        // A4尺寸 (595 x 842 points)
        const a4Width = 595;
        const a4Height = 842;
        
        // 处理左侧图像
        const leftImageBuffer = Buffer.from(leftImageData.split(',')[1], 'base64');
        const leftImage = sharp(leftImageBuffer);
        const leftMetadata = await leftImage.metadata();
        
        // 优化左侧图像 - 提高质量并适配A4
        const leftOptimized = await leftImage
            .resize({
                width: Math.min(a4Width * 2, leftMetadata.width * 2), // 2倍分辨率
                height: Math.min(a4Height * 2, leftMetadata.height * 2),
                fit: 'inside',
                withoutEnlargement: false
            })
            .png({ quality: 100 })
            .toBuffer();
        
        // 处理右侧图像
        const rightImageBuffer = Buffer.from(rightImageData.split(',')[1], 'base64');
        const rightImage = sharp(rightImageBuffer);
        const rightMetadata = await rightImage.metadata();
        
        // 优化右侧图像
        const rightOptimized = await rightImage
            .resize({
                width: Math.min(a4Width * 2, rightMetadata.width * 2),
                height: Math.min(a4Height * 2, rightMetadata.height * 2),
                fit: 'inside',
                withoutEnlargement: false
            })
            .png({ quality: 100 })
            .toBuffer();
        
        // 嵌入左侧图像到PDF
        const leftPngImage = await pdfDoc.embedPng(leftOptimized);
        const leftPage = pdfDoc.addPage([a4Width, a4Height]);
        
        // 计算左侧图像尺寸以填充页面
        const leftScale = Math.min(
            (a4Width - 40) / leftPngImage.width,  // 留20px边距
            (a4Height - 40) / leftPngImage.height
        );
        const leftFinalWidth = leftPngImage.width * leftScale;
        const leftFinalHeight = leftPngImage.height * leftScale;
        
        leftPage.drawImage(leftPngImage, {
            x: (a4Width - leftFinalWidth) / 2,
            y: (a4Height - leftFinalHeight) / 2,
            width: leftFinalWidth,
            height: leftFinalHeight,
        });
        
        // 嵌入右侧图像到PDF
        const rightPngImage = await pdfDoc.embedPng(rightOptimized);
        const rightPage = pdfDoc.addPage([a4Width, a4Height]);
        
        // 计算右侧图像尺寸以填充页面
        const rightScale = Math.min(
            (a4Width - 40) / rightPngImage.width,
            (a4Height - 40) / rightPngImage.height
        );
        const rightFinalWidth = rightPngImage.width * rightScale;
        const rightFinalHeight = rightPngImage.height * rightScale;
        
        rightPage.drawImage(rightPngImage, {
            x: (a4Width - rightFinalWidth) / 2,
            y: (a4Height - rightFinalHeight) / 2,
            width: rightFinalWidth,
            height: rightFinalHeight,
        });
        
        // 生成PDF
        const pdfBytes = await pdfDoc.save();
        
        // 使用最安全的文件名策略 - 完全避免特殊字符
        const timestamp = Date.now();
        const safeFilename = `split-document-${timestamp}.pdf`;

        console.log('原始文件名:', filename);
        console.log('生成的安全文件名:', safeFilename);

        try {
            // 返回PDF文件，使用纯ASCII文件名
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename=${safeFilename}`);
            res.setHeader('Content-Length', pdfBytes.length);
            res.setHeader('Cache-Control', 'no-cache');
            res.send(Buffer.from(pdfBytes));

            console.log('PDF拆分完成，文件大小:', pdfBytes.length, 'bytes');
        } catch (headerError) {
            console.error('设置HTTP头部失败:', headerError);
            // 如果设置头部失败，使用最简单的方式
            res.writeHead(200, {
                'Content-Type': 'application/pdf',
                'Content-Disposition': 'attachment; filename=split-document.pdf'
            });
            res.end(Buffer.from(pdfBytes));
        }
        
    } catch (error) {
        console.error('PDF拆分错误:', error);
        console.error('错误堆栈:', error.stack);

        // 根据错误类型返回不同的状态码和消息
        let statusCode = 500;
        let errorMessage = error.message;

        if (error.message.includes('缺少图像数据')) {
            statusCode = 400;
            errorMessage = '请求数据不完整，缺少图像数据';
        } else if (error.message.includes('图像数据格式不正确')) {
            statusCode = 400;
            errorMessage = '图像数据格式错误，请重新生成';
        } else if (error.message.includes('Invalid character in header')) {
            statusCode = 500;
            errorMessage = '文件名处理失败，请重试';
        }

        res.status(statusCode).json({
            error: errorMessage,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// 健康检查API
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        message: 'PDF处理服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 获取服务器信息
app.get('/api/info', (req, res) => {
    res.json({
        name: 'PDF Split Server',
        version: '1.0.0',
        features: ['pdf-split', 'image-optimization'],
        maxFileSize: '50MB'
    });
});

// 启动服务器
app.listen(port, () => {
    console.log(`PDF处理服务器运行在 http://localhost:${port}`);
    console.log('支持的功能:');
    console.log('- POST /api/split-pdf-images - PDF图像拆分');
    console.log('- GET /api/health - 健康检查');
    console.log('- GET /api/info - 服务器信息');
});
