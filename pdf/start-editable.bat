@echo off
chcp 65001 >nul

echo 🚀 启动可编辑PDF拆分系统
echo ================================

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 安装依赖包...
    npm install
)

REM 启动服务器
echo 🌟 启动可编辑PDF拆分服务器...
echo 📍 服务器地址: http://localhost:3001
echo 📄 可编辑拆分页面: http://localhost:3001/index-editable.html
echo 📄 传统拆分页面: http://localhost:3001/index.html
echo.
echo 💡 使用 Ctrl+C 停止服务器
echo ================================

REM 启动服务器
node server-editable.js

pause
