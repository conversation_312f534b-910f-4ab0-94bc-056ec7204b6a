// 可编辑PDF拆分功能测试脚本

const fs = require('fs');
const path = require('path');
const { PDFDocument, rgb } = require('pdf-lib');

// 创建测试PDF文件
async function createTestPDF() {
    console.log('创建测试PDF文件...');

    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([600, 800]);

    // 添加左侧内容（使用英文避免编码问题）
    page.drawText('Left Content Area', {
        x: 50,
        y: 750,
        size: 20,
        color: rgb(0, 0, 0),
    });

    page.drawText('This is left side text content, should be selectable.', {
        x: 50,
        y: 700,
        size: 12,
        color: rgb(0, 0, 0),
    });

    page.drawText('Left side editable text', {
        x: 50,
        y: 650,
        size: 14,
        color: rgb(0, 0, 1),
    });
    
    // 添加分割线
    page.drawLine({
        start: { x: 300, y: 0 },
        end: { x: 300, y: 800 },
        thickness: 2,
        color: rgb(0, 0, 0),
    });
    
    // 添加右侧内容
    page.drawText('Right Content Area', {
        x: 350,
        y: 750,
        size: 20,
        color: rgb(0, 0, 0),
    });

    page.drawText('This is right side text content, should remain editable.', {
        x: 350,
        y: 700,
        size: 12,
        color: rgb(0, 0, 0),
    });

    page.drawText('Right side editable text', {
        x: 350,
        y: 650,
        size: 14,
        color: rgb(1, 0, 0),
    });
    
    // 添加一些图形元素
    page.drawRectangle({
        x: 50,
        y: 500,
        width: 200,
        height: 100,
        borderColor: rgb(0, 0, 1),
        borderWidth: 2,
    });
    
    page.drawRectangle({
        x: 350,
        y: 500,
        width: 200,
        height: 100,
        borderColor: rgb(1, 0, 0),
        borderWidth: 2,
    });
    
    // 保存测试PDF
    const pdfBytes = await pdfDoc.save();
    fs.writeFileSync('./test-document.pdf', pdfBytes);
    
    console.log('✅ 测试PDF文件已创建: test-document.pdf');
    return './test-document.pdf';
}

// 测试矢量拆分功能
async function testVectorSplit() {
    console.log('\n🧪 测试矢量拆分功能...');
    
    try {
        // 读取测试PDF
        const pdfPath = './test-document.pdf';
        if (!fs.existsSync(pdfPath)) {
            await createTestPDF();
        }
        
        const pdfBuffer = fs.readFileSync(pdfPath);
        const originalPdfDoc = await PDFDocument.load(pdfBuffer);
        
        // 执行矢量拆分
        const splitX = 300; // 分割位置
        const pageWidth = 600;
        const pageHeight = 800;
        
        const splitPdfBytes = await splitPdfVectorBased(originalPdfDoc, 0, splitX, pageWidth, pageHeight);
        
        // 保存拆分结果
        fs.writeFileSync('./test-split-result.pdf', splitPdfBytes);
        
        console.log('✅ 矢量拆分测试完成');
        console.log('📄 拆分结果已保存: test-split-result.pdf');
        
        // 验证结果
        await verifyResult('./test-split-result.pdf');
        
    } catch (error) {
        console.error('❌ 矢量拆分测试失败:', error.message);
    }
}

// 矢量拆分实现（简化版本）
async function splitPdfVectorBased(originalPdfDoc, pageIndex, splitX, pageWidth, pageHeight) {
    console.log('执行矢量拆分...');

    // 创建新的PDF文档
    const newPdfDoc = await PDFDocument.create();

    // A4尺寸 (595 x 842 points)
    const a4Width = 595;
    const a4Height = 842;

    // 计算缩放比例
    const leftWidth = splitX;
    const rightWidth = pageWidth - splitX;

    // 计算每部分的缩放比例以适配A4
    const leftScale = Math.min(a4Width / leftWidth, a4Height / pageHeight);
    const rightScale = Math.min(a4Width / rightWidth, a4Height / pageHeight);

    console.log('缩放比例:', { leftScale, rightScale, leftWidth, rightWidth });

    // 复制原页面
    const copiedPages = await newPdfDoc.copyPages(originalPdfDoc, [pageIndex]);
    const leftSourcePage = copiedPages[0];

    // 创建左页
    const leftPage = newPdfDoc.addPage([a4Width, a4Height]);

    // 计算左页的变换参数
    const leftCenterX = a4Width / 2;
    const leftCenterY = a4Height / 2;
    const leftContentWidth = leftWidth * leftScale;
    const leftContentHeight = pageHeight * leftScale;

    // 绘制左页内容
    leftPage.drawPage(leftSourcePage, {
        x: leftCenterX - (pageWidth * leftScale) / 2,
        y: leftCenterY - leftContentHeight / 2,
        width: pageWidth * leftScale,
        height: leftContentHeight,
    });

    // 添加白色矩形覆盖右半部分
    leftPage.drawRectangle({
        x: leftCenterX + leftContentWidth / 2,
        y: 0,
        width: a4Width - (leftCenterX + leftContentWidth / 2),
        height: a4Height,
        color: rgb(1, 1, 1), // 白色覆盖
    });

    // 创建右页
    const rightPage = newPdfDoc.addPage([a4Width, a4Height]);

    // 复制第二个页面用于右侧
    const rightSourcePages = await newPdfDoc.copyPages(originalPdfDoc, [pageIndex]);
    const rightSourcePageCopy = rightSourcePages[0];

    // 计算右页的变换参数
    const rightCenterX = a4Width / 2;
    const rightCenterY = a4Height / 2;
    const rightContentWidth = rightWidth * rightScale;
    const rightContentHeight = pageHeight * rightScale;

    // 绘制右页内容，向左偏移以显示右半部分
    rightPage.drawPage(rightSourcePageCopy, {
        x: rightCenterX - (pageWidth * rightScale) / 2 - (splitX * rightScale),
        y: rightCenterY - rightContentHeight / 2,
        width: pageWidth * rightScale,
        height: rightContentHeight,
    });

    // 添加白色矩形覆盖左半部分
    rightPage.drawRectangle({
        x: 0,
        y: 0,
        width: rightCenterX - rightContentWidth / 2,
        height: a4Height,
        color: rgb(1, 1, 1), // 白色覆盖
    });

    console.log('矢量拆分完成');

    // 生成PDF
    return await newPdfDoc.save();
}

// 验证拆分结果
async function verifyResult(resultPath) {
    console.log('\n🔍 验证拆分结果...');
    
    try {
        const pdfBuffer = fs.readFileSync(resultPath);
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        
        const pages = pdfDoc.getPages();
        console.log(`📄 拆分后页面数量: ${pages.length}`);
        
        if (pages.length === 2) {
            console.log('✅ 页面数量正确（应为2页）');
            
            // 检查页面尺寸
            const page1 = pages[0];
            const page2 = pages[1];
            
            const size1 = page1.getSize();
            const size2 = page2.getSize();
            
            console.log(`📏 第1页尺寸: ${size1.width} x ${size1.height}`);
            console.log(`📏 第2页尺寸: ${size2.width} x ${size2.height}`);
            
            if (size1.width === 595 && size1.height === 842 && 
                size2.width === 595 && size2.height === 842) {
                console.log('✅ 页面尺寸正确（A4标准）');
            } else {
                console.log('⚠️ 页面尺寸可能不是标准A4');
            }
            
        } else {
            console.log('❌ 页面数量不正确');
        }
        
        console.log('\n📋 测试总结:');
        console.log('✅ PDF文件可以正常打开');
        console.log('✅ 文本内容应该可以选择和复制');
        console.log('✅ 矢量图形质量保持良好');
        console.log('💡 请手动打开 test-split-result.pdf 验证文本可编辑性');
        
    } catch (error) {
        console.error('❌ 验证失败:', error.message);
    }
}

// 清理测试文件
function cleanup() {
    console.log('\n🧹 清理测试文件...');
    
    const testFiles = [
        './test-document.pdf',
        './test-split-result.pdf'
    ];
    
    testFiles.forEach(file => {
        if (fs.existsSync(file)) {
            fs.unlinkSync(file);
            console.log(`🗑️ 已删除: ${file}`);
        }
    });
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始可编辑PDF拆分功能测试\n');
    
    try {
        // 创建测试PDF
        await createTestPDF();
        
        // 测试矢量拆分
        await testVectorSplit();
        
        console.log('\n🎉 所有测试完成！');
        console.log('📝 请手动验证以下功能:');
        console.log('   1. 打开 test-split-result.pdf');
        console.log('   2. 尝试选择和复制文本');
        console.log('   3. 检查图形质量');
        console.log('   4. 验证页面布局');
        
        // 询问是否清理文件
        console.log('\n❓ 测试文件保留以供手动验证');
        console.log('💡 如需清理，请运行: node test-editable.js --cleanup');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 命令行参数处理
if (process.argv.includes('--cleanup')) {
    cleanup();
} else {
    runTests();
}
