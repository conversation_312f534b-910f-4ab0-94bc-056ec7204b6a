// 文件名处理测试脚本

// 服务端文件名清理函数（复制自server-simple.js）
function sanitizeFilename(filename) {
    if (!filename) return 'split-document.pdf';

    // 移除或替换HTTP头部不安全的字符，但保留中文字符
    let safeName = filename
        .replace(/[\r\n\t]/g, '_')      // 替换换行符和制表符
        .replace(/[<>:"|?*]/g, '_')     // 替换文件系统不允许的字符
        .replace(/\\/g, '_')            // 替换反斜杠
        .replace(/\s+/g, '_')           // 替换空格为下划线
        .replace(/_{2,}/g, '_')         // 合并多个下划线
        .trim();

    // 移除开头和结尾的下划线
    safeName = safeName.replace(/^_+|_+$/g, '');

    // 确保文件名不为空且有正确的扩展名
    if (!safeName || safeName === '_') {
        safeName = 'split-document';
    }

    // 确保有.pdf扩展名
    if (!safeName.toLowerCase().endsWith('.pdf')) {
        safeName += '.pdf';
    }

    // 限制文件名长度（考虑中文字符占用更多字节）
    if (safeName.length > 80) {
        const nameWithoutExt = safeName.substring(0, safeName.lastIndexOf('.pdf'));
        safeName = nameWithoutExt.substring(0, 76) + '.pdf';
    }

    return safeName;
}

// 前端文件名清理函数（复制自script.js）
function sanitizeClientFilename(filename) {
    if (!filename) return 'document';
    
    // 移除或替换不安全的字符，保持中文字符
    let safeName = filename
        .replace(/[<>:"/\\|?*]/g, '_')   // 替换文件系统不允许的字符
        .replace(/\s+/g, '_')           // 替换空格为下划线
        .replace(/_{2,}/g, '_')         // 合并多个下划线
        .trim();
    
    // 确保文件名不为空
    if (!safeName || safeName === '_') {
        safeName = 'document';
    }
    
    // 限制文件名长度（考虑中文字符）
    if (safeName.length > 50) {
        safeName = safeName.substring(0, 50);
    }
    
    return safeName;
}

// 测试用例
const testCases = [
    // 正常文件名
    '测试文档.pdf',
    'test-document.pdf',
    'normal_file.pdf',
    
    // 包含特殊字符的文件名
    '第二册 面积.pdf',
    'test<>file.pdf',
    'file:with|special*chars.pdf',
    'file"with\'quotes.pdf',
    
    // 包含空格的文件名
    'file with spaces.pdf',
    '  file  with  multiple  spaces  .pdf',
    
    // 长文件名
    'this_is_a_very_long_filename_that_exceeds_the_normal_length_limit_and_should_be_truncated_properly.pdf',
    
    // 边界情况
    '',
    null,
    undefined,
    '.pdf',
    '_____.pdf',
    
    // 中文文件名
    '中文文档测试.pdf',
    '测试文档_拆分.pdf',
    '复杂的中文文件名包含特殊字符<>:"/\\|?*.pdf'
];

console.log('🧪 开始测试文件名处理函数...\n');

testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: "${testCase}"`);
    
    try {
        // 前端处理
        const clientProcessed = sanitizeClientFilename(
            testCase ? testCase.replace('.pdf', '') : testCase
        );
        const clientFilename = `${clientProcessed}_split.pdf`;
        
        // 服务端处理
        const serverProcessed = sanitizeFilename(clientFilename);
        
        console.log(`  前端处理: "${clientFilename}"`);
        console.log(`  服务端处理: "${serverProcessed}"`);
        
        // 验证结果
        if (serverProcessed && serverProcessed.endsWith('.pdf') && serverProcessed.length <= 100) {
            console.log(`  ✅ 处理成功\n`);
        } else {
            console.log(`  ❌ 处理失败\n`);
        }
        
    } catch (error) {
        console.log(`  ❌ 处理出错: ${error.message}\n`);
    }
});

console.log('🎉 文件名处理测试完成！');

// 验证HTTP头部安全性
console.log('\n🔒 验证HTTP头部安全性...');

const problematicNames = [
    '第二册 面积_split.pdf',
    'test\nfile_split.pdf',
    'file\rname_split.pdf',
    'file\tname_split.pdf'
];

problematicNames.forEach(name => {
    const safe = sanitizeFilename(name);
    const hasNewlines = /[\r\n\t]/.test(safe);
    const hasHttpUnsafeChars = /[<>:"|\\?*\r\n\t]/.test(safe);

    console.log(`原始: "${name}"`);
    console.log(`处理后: "${safe}"`);
    console.log(`安全性: ${!hasNewlines && !hasHttpUnsafeChars ? '✅ 安全' : '❌ 不安全'}\n`);
});

console.log('✅ HTTP头部安全性验证完成！');
