// 简单的PDF拆分测试

const fs = require('fs');
const { PDFDocument, rgb } = require('pdf-lib');

async function createAndSplitTest() {
    console.log('🧪 创建简单测试PDF...');
    
    // 创建原始PDF
    const originalDoc = await PDFDocument.create();
    const page = originalDoc.addPage([600, 400]);
    
    // 添加左侧内容
    page.drawText('LEFT SIDE TEXT', {
        x: 50,
        y: 300,
        size: 20,
        color: rgb(0, 0, 1),
    });
    
    page.drawText('This text should be selectable after split', {
        x: 50,
        y: 250,
        size: 12,
        color: rgb(0, 0, 0),
    });
    
    // 添加分割线
    page.drawLine({
        start: { x: 300, y: 0 },
        end: { x: 300, y: 400 },
        thickness: 2,
        color: rgb(0, 0, 0),
    });
    
    // 添加右侧内容
    page.drawText('RIGHT SIDE TEXT', {
        x: 350,
        y: 300,
        size: 20,
        color: rgb(1, 0, 0),
    });
    
    page.drawText('This text should also be selectable', {
        x: 350,
        y: 250,
        size: 12,
        color: rgb(0, 0, 0),
    });
    
    // 保存原始PDF
    const originalBytes = await originalDoc.save();
    fs.writeFileSync('./test-original.pdf', originalBytes);
    console.log('✅ 原始PDF已创建: test-original.pdf');
    
    // 执行拆分
    console.log('🔄 执行矢量拆分...');
    const splitBytes = await performVectorSplit(originalDoc, 300, 600, 400);
    
    // 保存拆分结果
    fs.writeFileSync('./test-split-vector.pdf', splitBytes);
    console.log('✅ 拆分PDF已创建: test-split-vector.pdf');
    
    // 验证结果
    await verifyResult('./test-split-vector.pdf');
}

async function performVectorSplit(sourceDoc, splitX, pageWidth, pageHeight) {
    // 创建新PDF
    const newDoc = await PDFDocument.create();
    
    // A4尺寸
    const a4Width = 595;
    const a4Height = 842;
    
    // 复制原页面
    const [sourcePage] = await newDoc.copyPages(sourceDoc, [0]);
    
    // 创建左页
    const leftPage = newDoc.addPage([a4Width, a4Height]);
    
    // 计算左侧缩放
    const leftWidth = splitX;
    const leftScale = Math.min(a4Width / leftWidth, a4Height / pageHeight);
    
    // 绘制左页（完整页面，然后用白色覆盖右侧）
    leftPage.drawPage(sourcePage, {
        x: 50,
        y: 100,
        width: pageWidth * leftScale,
        height: pageHeight * leftScale,
    });
    
    // 覆盖右侧
    leftPage.drawRectangle({
        x: 50 + (splitX * leftScale),
        y: 0,
        width: a4Width - (50 + splitX * leftScale),
        height: a4Height,
        color: rgb(1, 1, 1),
    });
    
    // 创建右页
    const rightPage = newDoc.addPage([a4Width, a4Height]);
    
    // 复制另一个页面用于右侧
    const [sourcePageRight] = await newDoc.copyPages(sourceDoc, [0]);
    
    // 计算右侧缩放
    const rightWidth = pageWidth - splitX;
    const rightScale = Math.min(a4Width / rightWidth, a4Height / pageHeight);
    
    // 绘制右页（向左偏移显示右半部分）
    rightPage.drawPage(sourcePageRight, {
        x: 50 - (splitX * rightScale),
        y: 100,
        width: pageWidth * rightScale,
        height: pageHeight * rightScale,
    });
    
    // 覆盖左侧
    rightPage.drawRectangle({
        x: 0,
        y: 0,
        width: 50,
        height: a4Height,
        color: rgb(1, 1, 1),
    });
    
    return await newDoc.save();
}

async function verifyResult(filePath) {
    console.log('🔍 验证拆分结果...');
    
    try {
        const pdfBytes = fs.readFileSync(filePath);
        const doc = await PDFDocument.load(pdfBytes);
        
        const pages = doc.getPages();
        console.log(`📄 页面数量: ${pages.length} (应为2)`);
        
        if (pages.length === 2) {
            console.log('✅ 页面数量正确');
            
            const page1Size = pages[0].getSize();
            const page2Size = pages[1].getSize();
            
            console.log(`📏 第1页: ${page1Size.width} x ${page1Size.height}`);
            console.log(`📏 第2页: ${page2Size.width} x ${page2Size.height}`);
            
            console.log('\n🎉 测试完成！');
            console.log('📝 请手动验证:');
            console.log('   1. 打开 test-split-vector.pdf');
            console.log('   2. 检查是否有2页');
            console.log('   3. 尝试选择文本');
            console.log('   4. 验证文本可复制');
        } else {
            console.log('❌ 页面数量不正确');
        }
        
    } catch (error) {
        console.error('❌ 验证失败:', error.message);
    }
}

// 运行测试
createAndSplitTest().catch(console.error);
